<template>
  <div
    class="article-edit"
    @dragover.prevent
    @drop.prevent="handleDrop"
    :class="{ 'drag-over': isDragOver }"
    @dragenter="handleDragEnter"
    @dragleave="handleDragLeave"
  >
    <div class="page-header">
      <h1>{{ isEdit ? '编辑文章' : '新建文章' }}</h1>
      <div>
        <el-button @click="$router.back()">返回</el-button>
        <el-button @click="togglePreview" :type="showPreview ? 'warning' : 'info'">
          {{ showPreview ? '隐藏预览' : '显示预览' }}
        </el-button>
        <el-button type="primary" @click="saveArticle" :loading="saving">
          保存
        </el-button>
      </div>
    </div>
    
    <el-row :gutter="20" v-if="showPreview">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>编辑文章</span>
          </template>
          <el-form :model="articleForm" :rules="rules" ref="formRef" label-width="100px">
            <el-form-item label="标题" prop="title">
              <el-input v-model="articleForm.title" placeholder="请输入文章标题" />
            </el-form-item>

            <el-form-item label="Slug" prop="slug">
              <el-input v-model="articleForm.slug" placeholder="URL友好的标识符" />
            </el-form-item>

            <el-form-item label="描述">
              <el-input
                v-model="articleForm.description"
                type="textarea"
                :rows="3"
                placeholder="文章简介"
              />
            </el-form-item>

            <el-form-item label="内容" prop="content">
              <MarkdownEditor v-model="articleForm.content" :rows="15" />
            </el-form-item>

            <el-form-item label="状态">
              <el-select v-model="articleForm.status" style="width: 100%">
                <el-option label="草稿" value="draft" />
                <el-option label="已发布" value="published" />
              </el-select>
            </el-form-item>

            <el-form-item label="特色文章">
              <el-switch v-model="articleForm.featured" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :span="12">
        <ArticlePreview :article="articleForm" />
      </el-col>
    </el-row>

    <el-card v-else>
      <el-form :model="articleForm" :rules="rules" ref="formRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="标题" prop="title">
              <el-input v-model="articleForm.title" placeholder="请输入文章标题" />
            </el-form-item>
            
            <el-form-item label="Slug" prop="slug">
              <el-input v-model="articleForm.slug" placeholder="URL友好的标识符" />
            </el-form-item>
            
            <el-form-item label="描述">
              <el-input
                v-model="articleForm.description"
                type="textarea"
                :rows="3"
                placeholder="文章简介"
              />
            </el-form-item>
            
            <el-form-item label="内容" prop="content">
              <MarkdownEditor v-model="articleForm.content" :rows="20" />
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="状态">
              <el-select v-model="articleForm.status" style="width: 100%">
                <el-option label="草稿" value="draft" />
                <el-option label="已发布" value="published" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="特色文章">
              <el-switch v-model="articleForm.featured" />
            </el-form-item>
            
            <el-form-item label="封面图片">
              <el-input v-model="articleForm.cover_image" placeholder="图片URL" />
            </el-form-item>
            
            <el-form-item label="分类">
              <el-select
                v-model="articleForm.categories"
                multiple
                placeholder="选择分类"
                style="width: 100%"
              >
                <el-option
                  v-for="category in categories"
                  :key="category.id"
                  :label="category.name"
                  :value="category.id"
                />
              </el-select>
            </el-form-item>
            
            <el-form-item label="标签">
              <el-select
                v-model="articleForm.tags"
                multiple
                placeholder="选择标签"
                style="width: 100%"
              >
                <el-option
                  v-for="tag in tags"
                  :key="tag.id"
                  :label="tag.name"
                  :value="tag.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="辅助介绍">
              <el-input
                v-model="articleForm.auxiliary_content"
                type="textarea"
                :rows="4"
                placeholder="文章辅助介绍文字，将显示在前端侧边栏"
              />
            </el-form-item>

            <el-form-item label="文章配图">
              <div class="article-images-manager">
                <el-button
                  type="primary"
                  size="small"
                  @click="showImageSelector = true"
                  style="margin-bottom: 10px;"
                >
                  添加配图
                </el-button>

                <div v-if="selectedArticleImages.length > 0" class="selected-images">
                  <div
                    v-for="(image, index) in selectedArticleImages"
                    :key="image.id"
                    class="image-item"
                  >
                    <img :src="getImageUrl(image)" :alt="image.original_name" />
                    <div class="image-actions">
                      <el-button
                        size="small"
                        type="danger"
                        @click="removeArticleImage(index)"
                      >
                        移除
                      </el-button>
                    </div>
                  </div>
                </div>

                <div v-else class="no-images">
                  <p>暂无配图</p>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 图片选择器 -->
    <ImageSelector
      v-model="showImageSelector"
      :multiple="true"
      @select="handleArticleImageSelect"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import api from '@/api'
import MarkdownEditor from '@/components/MarkdownEditor.vue'
import ArticlePreview from '@/components/ArticlePreview.vue'
import ImageSelector from '@/components/ImageSelector.vue'

const route = useRoute()
const router = useRouter()

const isEdit = computed(() => !!route.params.id)
const saving = ref(false)
const showPreview = ref(false)
const formRef = ref()
const categories = ref([])
const tags = ref([])
const isDragOver = ref(false)
const dragCounter = ref(0)
const showImageSelector = ref(false)
const selectedArticleImages = ref([])

const articleForm = reactive({
  title: '',
  slug: '',
  description: '',
  content: '',
  cover_image: '',
  auxiliary_content: '',
  status: 'draft',
  featured: false,
  categories: [],
  tags: [],
  articleImages: []
})

const rules = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' }
  ],
  slug: [
    { required: true, message: '请输入Slug', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入文章内容', trigger: 'blur' }
  ]
}

// 获取分类和标签
const fetchCategoriesAndTags = async () => {
  try {
    const [categoriesRes, tagsRes] = await Promise.all([
      api.get('/categories'),
      api.get('/tags')
    ])
    
    categories.value = categoriesRes.data.data
    tags.value = tagsRes.data.data
  } catch (error) {
    console.error('获取分类和标签失败:', error)
  }
}

// 获取文章详情（编辑模式）
const fetchArticle = async () => {
  if (!isEdit.value) return
  
  try {
    const response = await api.get(`/articles/${route.params.id}`)
    const article = response.data.data
    
    Object.keys(articleForm).forEach(key => {
      if (key === 'categories') {
        articleForm[key] = article.categories?.map(c => c.id) || []
      } else if (key === 'tags') {
        articleForm[key] = article.tags?.map(t => t.id) || []
      } else if (key === 'articleImages') {
        // 不处理，由selectedArticleImages处理
      } else {
        articleForm[key] = article[key] || articleForm[key]
      }
    })

    // 设置文章配图
    selectedArticleImages.value = article.articleImages || []
  } catch (error) {
    console.error('获取文章详情失败:', error)
    ElMessage.error('获取文章详情失败')
  }
}

// 保存文章
const saveArticle = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) return

    saving.value = true
    try {
      // 计算阅读统计
      const stats = calculateReadingStats(articleForm.content)

      // 准备保存数据
      const saveData = {
        ...articleForm,
        word_count: stats.wordCount,
        reading_time: stats.readingTime,
        articleImages: selectedArticleImages.value.map(img => img.id)
      }

      if (isEdit.value) {
        await api.put(`/articles/${route.params.id}`, saveData)
        ElMessage.success('文章更新成功')
      } else {
        await api.post('/articles', saveData)
        ElMessage.success('文章创建成功')
      }

      router.push('/articles')
    } catch (error) {
      console.error('保存文章失败:', error)
      ElMessage.error('保存失败')
    } finally {
      saving.value = false
    }
  })
}

// 切换预览模式
const togglePreview = () => {
  showPreview.value = !showPreview.value
}

// 拖拽处理函数
const handleDragEnter = (e) => {
  e.preventDefault()
  dragCounter.value++
  if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
    isDragOver.value = true
  }
}

const handleDragLeave = (e) => {
  e.preventDefault()
  dragCounter.value--
  if (dragCounter.value === 0) {
    isDragOver.value = false
  }
}

const handleDrop = async (e) => {
  e.preventDefault()
  isDragOver.value = false
  dragCounter.value = 0

  const files = Array.from(e.dataTransfer.files)
  const imageFiles = files.filter(file => file.type.startsWith('image/'))

  if (imageFiles.length === 0) {
    ElMessage.warning('请拖拽图片文件')
    return
  }

  if (imageFiles.length > 5) {
    ElMessage.warning('一次最多上传5张图片')
    return
  }

  // 上传图片
  for (const file of imageFiles) {
    await uploadImageFile(file)
  }
}

// 上传单个图片文件
const uploadImageFile = async (file) => {
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error(`图片 ${file.name} 大小超过10MB`)
    return
  }

  const formData = new FormData()
  formData.append('image', file)

  try {
    const response = await api.post('/images/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    if (response.data.success) {
      const image = response.data.data
      const alt = image.alt_text || image.filename || '图片'

      // 在光标位置插入图片Markdown
      const imageMarkdown = `![${alt}](${image.url})\n`
      articleForm.content += imageMarkdown

      ElMessage.success(`图片 ${file.name} 上传成功`)
    } else {
      ElMessage.error(`图片 ${file.name} 上传失败`)
    }
  } catch (error) {
    console.error('上传图片失败:', error)
    ElMessage.error(`图片 ${file.name} 上传失败`)
  }
}

// 文章配图相关方法
const getImageUrl = (image) => {
  return image.url
}

const handleArticleImageSelect = (selectedImages) => {
  if (Array.isArray(selectedImages)) {
    selectedArticleImages.value = [...selectedArticleImages.value, ...selectedImages]
  } else if (selectedImages) {
    selectedArticleImages.value.push(selectedImages)
  }
  showImageSelector.value = false
}

const removeArticleImage = (index) => {
  selectedArticleImages.value.splice(index, 1)
}

// 计算阅读时间和字数
const calculateReadingStats = (content) => {
  const wordCount = content.replace(/[^\u4e00-\u9fa5\w]/g, '').length
  const readingTime = Math.ceil(wordCount / 200) // 假设每分钟阅读200字
  return { wordCount, readingTime }
}

onMounted(() => {
  fetchCategoriesAndTags()
  fetchArticle()
})
</script>

<style scoped>
.article-edit {
  position: relative;
  transition: all 0.3s ease;
}

.article-edit.drag-over {
  background-color: rgba(64, 158, 255, 0.1);
  border: 2px dashed #409eff;
  border-radius: 8px;
}

.article-edit.drag-over::after {
  content: '拖拽图片到此处上传';
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(64, 158, 255, 0.9);
  color: white;
  padding: 20px 40px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: bold;
  z-index: 9999;
  pointer-events: none;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}

.article-images-manager {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.selected-images {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.image-item {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #eee;
}

.image-item img {
  width: 100%;
  height: 80px;
  object-fit: cover;
  display: block;
}

.image-actions {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 2px;
}

.image-actions .el-button {
  padding: 2px 6px;
  font-size: 12px;
}

.no-images {
  text-align: center;
  color: #999;
  padding: 20px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  margin-top: 10px;
}
</style>
